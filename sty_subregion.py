import pickle
import numpy as np
import pandas as pd
from tqdm import tqdm
import vtk
from vtk.util import numpy_support
from typing import List, Dict, Any
import os
import re


def load_and_process_data(filename: str) -> List[Dict[str, Any]]:
    """
    更稳健的文件加载方法，完全按照原始格式解析
    """
    with open(filename, 'r') as f:
        content = f.read()

    # 使用正则表达式精确分割区块
    blocks = re.split(r'\[Name\]\s*', content)[1:]

    regions = []
    offset = 0
    for block in tqdm(blocks, desc="处理区域数据", unit="region"):
        # 使用更安全的方式分割数据段
        data_parts = re.split(r'\[Data\]\s*', block, maxsplit=1)
        if len(data_parts) < 2:
            continue

        name = data_parts[0].strip()
        remaining = data_parts[1]

        # 分割面和数据
        face_parts = re.split(r'\[Faces\]\s*', remaining, maxsplit=1)
        if len(face_parts) < 2:
            continue

        data_section = face_parts[0]
        faces_section = face_parts[1]

        # 处理数据部分
        data_lines = [line.strip() for line in data_section.split('\n')
                      if line.strip()]
        header = [h.strip() for h in data_lines[0].split(',')]

        data = np.array([list(map(float, line.split(',')))
                         for line in data_lines[1:]])
        df = pd.DataFrame(data, columns=header)

        # 处理面数据
        faces = []
        ori_faces = []
        for line in faces_section.split('\n'):
            line = line.strip()
            if line:
                try:
                    ori_face = list(map(int, line.split(',')))
                    face = [(it + offset) for it in ori_face]
                    ori_faces.append(ori_face)
                    faces.append(face)
                except (ValueError, IndexError):
                    continue
        offset += len(data)
        regions.append({
            "name": name,
            "data": df,
            "faces": faces,
            "ori_faces": ori_faces,
            "points": df[['X [ m ]', 'Y [ m ]', 'Z [ m ]']].values.astype(
                np.float32)
        })
    print("数据加载处理完成，共处理了", len(regions), "个区域")
    return regions


def create_outlet_streamlines(regions: List[Dict[str, Any]],
                              output_dir: str = "outlet_streamlines"):
    """
    创建outlet子区域的流线可视化
    - 三维区域：Subdomain outlet
    - 入口平面：latooutlet Side 2
    - 出口平面：outlettoo
    - 区域管道表面：outlet Default
    """
    print("正在创建outlet子区域流线可视化...")

    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)

    # 建立区域映射
    region_map = {}
    for region in regions:
        region_map[region["name"]] = region

    # 检查必要的outlet相关区域是否存在
    required_regions = ["Subdomain outlet", "latooutlet Side 2",
                        "outlettoo", "outlet Default"]
    missing_regions = []
    for req in required_regions:
        if req not in region_map:
            missing_regions.append(req)

    if missing_regions:
        print(f"警告：缺失以下outlet相关区域: {missing_regions}")
        print(f"可用区域: {list(region_map.keys())}")
        return

    # 合并outlet相关区域的数据用于流线追踪
    outlet_regions = ["Subdomain outlet", "latooutlet Side 2",
                      "outlettoo", "outlet Default"]
    all_points = []
    all_data = []
    all_faces = []

    for region_name in outlet_regions:
        if region_name in region_map:
            region = region_map[region_name]
            all_points.append(region["points"])
            all_data.append(region["data"])
            all_faces.extend(region["faces"])

    # 合并数据
    global_points = np.vstack(all_points)
    global_df = pd.concat(all_data, ignore_index=True)

    print(f"合并后的outlet区域数据点数: {len(global_points)}")

    # 创建VTK非结构化网格
    grid = vtk.vtkUnstructuredGrid()

    # 添加点
    points_vtk = vtk.vtkPoints()
    for point in global_points:
        points_vtk.InsertNextPoint(point[0], point[1], point[2])
    grid.SetPoints(points_vtk)

    # 添加单元（面）
    for face in all_faces:
        if len(face) >= 3:  # 确保是有效的面
            if len(face) == 3:
                cell = vtk.vtkTriangle()
            elif len(face) == 4:
                cell = vtk.vtkQuad()
            else:
                cell = vtk.vtkPolygon()
                cell.GetPointIds().SetNumberOfIds(len(face))

            for i, point_id in enumerate(face):
                cell.GetPointIds().SetId(i, point_id)
            grid.InsertNextCell(cell.GetCellType(), cell.GetPointIds())

    # 添加速度矢量数据
    velocity_cols = ['Velocity u [ m s^-1 ]', 'Velocity v [ m s^-1 ]',
                     'Velocity w [ m s^-1 ]']
    if all(col in global_df.columns for col in velocity_cols):
        velocity = global_df[velocity_cols].values.astype(np.float32)
        velocity_array = numpy_support.numpy_to_vtk(velocity)
        velocity_array.SetName("Velocity")
        velocity_array.SetNumberOfComponents(3)
        grid.GetPointData().SetVectors(velocity_array)
        grid.GetPointData().SetActiveVectors("Velocity")
        print("已添加速度矢量数据")
    else:
        print("警告：未找到速度数据列")
        return

    # 添加压强数据（如果存在）
    pressure_col = 'Pressure [ Pa ]'
    if pressure_col in global_df.columns:
        pressure = global_df[pressure_col].values.astype(np.float32)
        pressure_array = numpy_support.numpy_to_vtk(pressure)
        pressure_array.SetName("Pressure")
        grid.GetPointData().SetScalars(pressure_array)
        print("已添加压强标量数据")

    print(f"网格点数: {grid.GetNumberOfPoints()}")
    print(f"网格单元数: {grid.GetNumberOfCells()}")

    # 创建渲染器
    renderer = vtk.vtkRenderer()

    # ================= 创建流线 =================
    # 使用入口平面 "latooutlet Side 2" 进行均匀采样
    inlet_region = region_map["latooutlet Side 2"]
    inlet_points = inlet_region["points"]

    print(f"入口平面 'latooutlet Side 2' 点数: {len(inlet_points)}")

    # 计算入口平面的边界
    min_coords = inlet_points.min(axis=0)
    max_coords = inlet_points.max(axis=0)

    print(f"入口平面边界: X[{min_coords[0]:.3f}, {max_coords[0]:.3f}], "
          f"Y[{min_coords[1]:.3f}, {max_coords[1]:.3f}], "
          f"Z[{min_coords[2]:.3f}, {max_coords[2]:.3f}]")

    # 在入口平面创建均匀网格种子点
    grid_density = 3  # 3x3网格，最少种子点数量以提高稳定性
    seeds = vtk.vtkPolyData()
    seed_points = vtk.vtkPoints()

    # 确定主要变化的两个维度（排除变化最小的维度）
    coord_ranges = max_coords - min_coords
    sorted_dims = np.argsort(coord_ranges)

    # 使用变化最大的两个维度创建网格
    dim1, dim2 = sorted_dims[1], sorted_dims[2]  # 变化较大的两个维度
    fixed_dim = sorted_dims[0]  # 变化最小的维度

    for i in range(grid_density):
        for j in range(grid_density):
            # 在两个主要维度上均匀分布
            coord = min_coords.copy()
            coord[dim1] = (min_coords[dim1] +
                           (max_coords[dim1] - min_coords[dim1]) *
                           i / (grid_density - 1))
            coord[dim2] = (min_coords[dim2] +
                           (max_coords[dim2] - min_coords[dim2]) *
                           j / (grid_density - 1))
            coord[fixed_dim] = ((min_coords[fixed_dim] +
                                max_coords[fixed_dim]) / 2)  # 固定维度取中值

            seed_points.InsertNextPoint(coord[0], coord[1], coord[2])

    seeds.SetPoints(seed_points)

    # 创建顶点单元
    verts = vtk.vtkCellArray()
    for i in range(seed_points.GetNumberOfPoints()):
        verts.InsertNextCell(1)
        verts.InsertCellPoint(i)
    seeds.SetVerts(verts)

    print(f"创建了 {seed_points.GetNumberOfPoints()} 个种子点进行流线追踪")

    # 验证网格数据
    if grid.GetNumberOfPoints() == 0:
        print("错误：网格中没有点数据")
        return

    if grid.GetPointData().GetVectors() is None:
        print("错误：网格中没有速度矢量数据")
        return

    # 检查速度数据的有效性
    velocity_data = grid.GetPointData().GetVectors()
    velocity_range = velocity_data.GetRange(-1)  # 获取所有分量的范围
    print(f"速度数据范围: {velocity_range}")

    if velocity_range[1] == 0:
        print("警告：速度数据全为零，可能无法生成有效流线")

    # 直接使用原始网格，避免复杂的转换
    print("直接使用原始非结构化网格进行流线追踪...")
    working_grid = grid

    # 创建流线追踪器
    integ = vtk.vtkRungeKutta4()
    streamer = vtk.vtkStreamTracer()
    streamer.SetIntegrator(integ)
    streamer.SetInputData(working_grid)  # 使用处理后的网格
    streamer.SetSourceData(seeds)

    # 设置非常保守的流线参数
    streamer.SetMaximumPropagation(100)  # 大幅减少最大传播距离
    streamer.SetInitialIntegrationStep(0.001)  # 非常小的初始步长
    streamer.SetMinimumIntegrationStep(1e-8)  # 非常小的最小步长
    streamer.SetMaximumIntegrationStep(0.01)  # 非常小的最大步长
    streamer.SetTerminalSpeed(1e-4)  # 较高的终止速度
    streamer.SetIntegrationDirectionToForward()  # 向前追踪

    # 设置严格的步数限制
    streamer.SetMaximumNumberOfSteps(500)

    # 设置积分步长单位为长度单位而不是单元长度
    streamer.SetIntegrationStepUnit(vtk.vtkStreamTracer.LENGTH_UNIT)

    print("开始流线追踪...")
    try:
        # 更新流线
        streamer.Update()
        streamlines = streamer.GetOutput()
        print("流线追踪完成")
    except Exception as e:
        print(f"流线追踪失败: {e}")
        return

    print(f"生成的流线数: {streamlines.GetNumberOfCells()}")
    print(f"流线点数: {streamlines.GetNumberOfPoints()}")

    # 创建流线的mapper和actor
    stream_mapper = vtk.vtkPolyDataMapper()
    stream_mapper.SetInputConnection(streamer.GetOutputPort())
    stream_mapper.ScalarVisibilityOff()  # 不显示标量颜色

    stream_actor = vtk.vtkActor()
    stream_actor.SetMapper(stream_mapper)
    stream_actor.GetProperty().SetColor(1, 0, 0)  # 红色流线
    stream_actor.GetProperty().SetLineWidth(2)
    stream_actor.GetProperty().SetOpacity(0.8)

    renderer.AddActor(stream_actor)

    # ================= 添加几何边界可视化 =================
    # 1. 入口平面 "latooutlet Side 2"
    inlet_poly = vtk.vtkPolyData()
    inlet_points_vtk = vtk.vtkPoints()

    for point in inlet_region["points"]:
        inlet_points_vtk.InsertNextPoint(point[0], point[1], point[2])
    inlet_poly.SetPoints(inlet_points_vtk)

    # 添加入口面的单元
    inlet_cells = vtk.vtkCellArray()
    for face in inlet_region["faces"]:
        if len(face) >= 3:
            inlet_cells.InsertNextCell(len(face))
            for point_id in face:
                # 使用原始面索引（相对于inlet区域）
                inlet_cells.InsertCellPoint(point_id)
    inlet_poly.SetPolys(inlet_cells)

    inlet_mapper = vtk.vtkPolyDataMapper()
    inlet_mapper.SetInputData(inlet_poly)

    inlet_actor = vtk.vtkActor()
    inlet_actor.SetMapper(inlet_mapper)
    inlet_actor.GetProperty().SetColor(0, 1, 0)  # 绿色入口平面
    inlet_actor.GetProperty().SetOpacity(0.6)

    renderer.AddActor(inlet_actor)

    # 2. 出口平面 "outlettoo"
    if "outlettoo" in region_map:
        outlet_region = region_map["outlettoo"]
        outlet_poly = vtk.vtkPolyData()
        outlet_points_vtk = vtk.vtkPoints()

        for point in outlet_region["points"]:
            outlet_points_vtk.InsertNextPoint(point[0], point[1], point[2])
        outlet_poly.SetPoints(outlet_points_vtk)

        # 添加出口面的单元
        outlet_cells = vtk.vtkCellArray()
        for face in outlet_region["faces"]:
            if len(face) >= 3:
                outlet_cells.InsertNextCell(len(face))
                for point_id in face:
                    outlet_cells.InsertCellPoint(point_id)
        outlet_poly.SetPolys(outlet_cells)

        outlet_mapper = vtk.vtkPolyDataMapper()
        outlet_mapper.SetInputData(outlet_poly)

        outlet_actor = vtk.vtkActor()
        outlet_actor.SetMapper(outlet_mapper)
        outlet_actor.GetProperty().SetColor(0, 0, 1)  # 蓝色出口平面
        outlet_actor.GetProperty().SetOpacity(0.6)

        renderer.AddActor(outlet_actor)
        print("已添加出口平面可视化")

    # 3. 管道表面 "outlet Default"
    if "outlet Default" in region_map:
        pipe_region = region_map["outlet Default"]
        pipe_poly = vtk.vtkPolyData()
        pipe_points_vtk = vtk.vtkPoints()

        for point in pipe_region["points"]:
            pipe_points_vtk.InsertNextPoint(point[0], point[1], point[2])
        pipe_poly.SetPoints(pipe_points_vtk)

        # 添加管道表面的单元
        pipe_cells = vtk.vtkCellArray()
        for face in pipe_region["faces"]:
            if len(face) >= 3:
                pipe_cells.InsertNextCell(len(face))
                for point_id in face:
                    pipe_cells.InsertCellPoint(point_id)
        pipe_poly.SetPolys(pipe_cells)

        pipe_mapper = vtk.vtkPolyDataMapper()
        pipe_mapper.SetInputData(pipe_poly)

        pipe_actor = vtk.vtkActor()
        pipe_actor.SetMapper(pipe_mapper)
        pipe_actor.GetProperty().SetColor(0.7, 0.7, 0.7)  # 灰色管道表面
        pipe_actor.GetProperty().SetOpacity(0.3)
        pipe_actor.GetProperty().SetRepresentationToWireframe()  # 线框模式

        renderer.AddActor(pipe_actor)
        print("已添加管道表面可视化")

    # ================= 设置渲染场景 =================
    renderer.SetBackground(0.1, 0.2, 0.3)  # 深蓝色背景
    renderer.ResetCamera()

    # 创建渲染窗口
    render_window = vtk.vtkRenderWindow()
    render_window.AddRenderer(renderer)
    render_window.SetSize(1200, 900)
    render_window.SetWindowName("Outlet子区域流线可视化")

    # 创建交互器
    render_window_interactor = vtk.vtkRenderWindowInteractor()
    render_window_interactor.SetRenderWindow(render_window)

    # 保存VTK文件
    vtk_file = os.path.join(output_dir, "outlet_streamlines.vtk")
    writer = vtk.vtkUnstructuredGridWriter()
    writer.SetFileName(vtk_file)
    writer.SetInputData(grid)
    writer.Write()
    print(f"VTK文件已保存: {vtk_file}")

    # 保存流线数据
    streamline_file = os.path.join(output_dir, "outlet_streamlines_data.vtk")
    stream_writer = vtk.vtkPolyDataWriter()
    stream_writer.SetFileName(streamline_file)
    stream_writer.SetInputData(streamlines)
    stream_writer.Write()
    print(f"流线数据已保存: {streamline_file}")

    # 保存截图
    window_to_image = vtk.vtkWindowToImageFilter()
    window_to_image.SetInput(render_window)
    window_to_image.Update()

    png_writer = vtk.vtkPNGWriter()
    png_file = os.path.join(output_dir, "outlet_streamlines.png")
    png_writer.SetFileName(png_file)
    png_writer.SetInputConnection(window_to_image.GetOutputPort())
    png_writer.Write()
    print(f"截图已保存: {png_file}")

    print("=" * 60)
    print("Outlet子区域流线可视化完成！")
    print(f"结果保存在: {output_dir}/")
    print("包含文件:")
    print("- outlet_streamlines.vtk (网格数据)")
    print("- outlet_streamlines_data.vtk (流线数据)")
    print("- outlet_streamlines.png (可视化截图)")
    print("=" * 60)

    # 启动交互式可视化
    try:
        render_window.Render()
        print("渲染完成，启动交互式窗口...")
        render_window_interactor.Start()
    except Exception as e:
        print(f"可视化启动失败: {e}")
        print("但文件已成功保存")


def main():
    """主执行函数"""
    # 加载和处理数据
    cache_file = os.path.join(os.getcwd(), "cache.pkl")
    if os.path.exists(cache_file):
        with open(cache_file, "rb") as f:
            regions = pickle.load(f)
    else:
        regions = load_and_process_data("steady_results.csv")
        with open(cache_file, "wb") as f:
            pickle.dump(regions, f)

    print([it['name'] for it in regions])

    # 只渲染outlet子区域流线： 三维区域是Subdomain outlet,
    # 入口平面是 latooutlet Side 2, 出口平面是 outlettoo,
    # 区域管道表面是outlet Default
    create_outlet_streamlines(regions)


if __name__ == "__main__":
    main()