{"id": "Downloads.sty_streamline", "path": "I:\\Downloads\\sty_streamline", "mtime": 1754387549, "size": 4096, "hash": "", "data_mtime": 1754298423, "dependencies": ["builtins", "abc", "typing"], "suppressed": [], "options": {"follow_imports_for_stubs": false, "disallow_untyped_calls": false, "warn_return_any": false, "local_partial_types": false, "disallow_subclassing_any": false, "disable_bytearray_promotion": false, "mypyc": false, "disallow_any_explicit": false, "disable_error_code": [], "disallow_untyped_decorators": false, "ignore_errors": false, "warn_unused_ignores": false, "bazel": false, "always_false": [], "warn_unreachable": false, "implicit_optional": false, "platform": "win32", "disallow_untyped_defs": false, "ignore_missing_imports": false, "always_true": [], "disallow_any_unimported": false, "enable_error_code": [], "check_untyped_defs": false, "disable_memoryview_promotion": false, "disallow_incomplete_defs": false, "strict_equality": false, "implicit_reexport": true, "follow_imports": "silent", "plugins": [], "disallow_any_expr": false, "extra_checks": false, "strict_concatenate": false, "old_type_inference": false, "allow_redefinition": false, "enabled_error_codes": [], "disabled_error_codes": [], "disallow_any_generics": false, "allow_untyped_globals": false, "strict_optional": true, "disallow_any_decorated": false, "warn_no_return": true}, "dep_prios": [5, 30, 30], "dep_lines": [1, 1, 1], "interface_hash": "650a5a6319a6600fb73f0cb97eb9e04ca80257a6c3b0450d2bb91038ca5ba557", "version_id": "1.10.1", "ignore_all": true, "plugin_data": null}