{"id": "Downloads.sty_streamline", "path": "I:\\Downloads\\sty_streamline", "mtime": 1754386946, "size": 4096, "hash": "", "data_mtime": 1754298423, "dependencies": ["builtins", "abc", "typing"], "suppressed": [], "options": {"warn_no_return": true, "disallow_any_expr": false, "disallow_untyped_defs": false, "disallow_any_decorated": false, "always_false": [], "extra_checks": false, "ignore_missing_imports": false, "disallow_any_generics": false, "disallow_subclassing_any": false, "allow_redefinition": false, "platform": "win32", "disabled_error_codes": [], "bazel": false, "disable_bytearray_promotion": false, "strict_concatenate": false, "strict_equality": false, "local_partial_types": false, "disallow_untyped_calls": false, "enable_error_code": [], "disallow_incomplete_defs": false, "warn_unused_ignores": false, "check_untyped_defs": false, "disallow_any_unimported": false, "disallow_untyped_decorators": false, "plugins": [], "warn_return_any": false, "enabled_error_codes": [], "disallow_any_explicit": false, "disable_error_code": [], "implicit_optional": false, "implicit_reexport": true, "allow_untyped_globals": false, "always_true": [], "warn_unreachable": false, "old_type_inference": false, "follow_imports": "silent", "strict_optional": true, "disable_memoryview_promotion": false, "mypyc": false, "follow_imports_for_stubs": false, "ignore_errors": false}, "dep_prios": [5, 30, 30], "dep_lines": [1, 1, 1], "interface_hash": "650a5a6319a6600fb73f0cb97eb9e04ca80257a6c3b0450d2bb91038ca5ba557", "version_id": "1.10.1", "ignore_all": true, "plugin_data": null}